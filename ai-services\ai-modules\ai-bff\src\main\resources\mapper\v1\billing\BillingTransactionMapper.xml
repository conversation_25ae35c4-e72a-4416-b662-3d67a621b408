<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingTransactionMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingTransaction" id="BillingTransactionResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="type" column="type"/>
        <result property="amount" column="amount"/>
        <result property="currencyId" column="currency_id"/>
        <result property="referenceId" column="reference_id"/>
        <result property="description" column="description"/>
        <result property="transactionTime" column="transaction_time"/>
    </resultMap>

    <sql id="selectBillingTransactionVo">
        select id, member_id, type, amount, currency_id, reference_id, description, transaction_time
        from a_billing_transaction
    </sql>

    <select id="selectTransactionsByMemberId" parameterType="Long" resultMap="BillingTransactionResult">
        <include refid="selectBillingTransactionVo"/>
        where member_id = #{memberId} and delete_time is null
        order by transaction_time desc
    </select>

    <insert id="insertTransaction" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingTransaction" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time, create_time, update_time)
        values (#{memberId}, #{type}, #{amount}, #{currencyId}, #{referenceId}, #{description}, #{transactionTime}, now(), now())
    </insert>

    <select id="selectTransactionsByMemberIdAndCondition" resultMap="BillingTransactionResult">
        <include refid="selectBillingTransactionVo"/>
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.type != null">
                and type = #{param.type}
            </if>
            <if test="param.startTime != null">
                and transaction_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and transaction_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and abs(amount) >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and abs(amount) &lt;= #{param.maxAmount}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and description like concat('%', #{param.keyword}, '%')
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by abs(amount)
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by transaction_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

    <select id="countTransactionsByMemberIdAndCondition" resultType="long">
        select count(*)
        from a_billing_transaction
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.type != null">
                and type = #{param.type}
            </if>
            <if test="param.startTime != null">
                and transaction_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and transaction_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and abs(amount) >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and abs(amount) &lt;= #{param.maxAmount}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and description like concat('%', #{param.keyword}, '%')
            </if>
        </where>
    </select>

    <!-- 直接查询交易记录VO，包含关联的货币信息和类型描述 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.BillingTransactionVo" id="BillingTransactionVoResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="typeDesc" column="type_desc"/>
        <result property="amount" column="amount"/>
        <result property="currencySymbol" column="currency_symbol"/>
        <result property="description" column="description"/>
        <result property="transactionTime" column="transaction_time"/>
    </resultMap>

    <select id="selectTransactionVosByMemberIdAndCondition" resultMap="BillingTransactionVoResult">
        select
            t.id,
            t.type,
            COALESCE(tt.dict_label, '未知类型') as type_desc,
            t.amount,
            COALESCE(c.symbol, '¥') as currency_symbol,
            t.description,
            t.transaction_time
        from a_billing_transaction t
        left join a_base_currency c on t.currency_id = c.id and c.delete_time is null
        left join sys_dict_data tt on tt.dict_type = 'transaction_type' and tt.dict_value = CAST(t.type as VARCHAR) and tt.status = '0'
        <where>
            and t.member_id = #{memberId}
            and t.delete_time is null
            <if test="param.type != null">
                and t.type = #{param.type}
            </if>
            <if test="param.startTime != null">
                and t.transaction_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and t.transaction_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and ABS(t.amount) >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and ABS(t.amount) &lt;= #{param.maxAmount}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                and t.description like concat('%', #{param.keyword}, '%')
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by ABS(t.amount)
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by t.transaction_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

</mapper>