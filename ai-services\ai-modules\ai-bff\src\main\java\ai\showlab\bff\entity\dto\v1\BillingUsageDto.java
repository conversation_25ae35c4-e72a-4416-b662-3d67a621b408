package ai.showlab.bff.entity.dto.v1;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用量记录 DTO
 *
 * <AUTHOR>
 */
@Data
public class BillingUsageDto {

    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    @NotNull(message = "模型ID不能为空")
    private Long modelId;
    
    // 假设会员等级信息可以从会话或其他地方获取，这里不传递

    @NotNull(message = "使用量不能为空")
    private BigDecimal amount;

    private Integer durationMs;

    private Integer resultSize;
} 