<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingBalanceMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingBalance" id="BillingBalanceResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="balance" column="balance"/>
        <result property="frozenAmount" column="frozen_amount"/>
        <result property="currencyId" column="currency_id"/>
        <result property="lowThreshold" column="low_threshold"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBillingBalanceVo">
        select id, member_id, balance, frozen_amount, currency_id, low_threshold, create_time, update_time
        from a_billing_balance
    </sql>

    <select id="selectBalanceByMemberId" parameterType="Long" resultMap="BillingBalanceResult">
        <include refid="selectBillingBalanceVo"/>
        where member_id = #{memberId} and delete_time is null
    </select>

    <insert id="insertBalance" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingBalance" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_balance (member_id, balance, frozen_amount, currency_id, low_threshold, create_time, update_time)
        values (#{memberId}, #{balance}, #{frozenAmount}, #{currencyId}, #{lowThreshold}, now(), now())
    </insert>

    <update id="updateBalance">
        update a_billing_balance
        set balance = balance + #{changeAmount},
            update_time = now()
        where member_id = #{memberId}
          and delete_time is null
    </update>

    <update id="updateFrozenAmount">
        update a_billing_balance
        set frozen_amount = frozen_amount + #{changeAmount},
            update_time = now()
        where member_id = #{memberId}
          and delete_time is null
    </update>

    <!-- 直接查询余额VO，包含关联的货币信息 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.BillingBalanceVo" id="BillingBalanceVoResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="balance" column="balance"/>
        <result property="frozenAmount" column="frozen_amount"/>
        <result property="totalBalance" column="total_balance"/>
        <result property="currencySymbol" column="currency_symbol"/>
        <result property="currencyCode" column="currency_code"/>
        <result property="lowThreshold" column="low_threshold"/>
        <result property="isLowBalance" column="is_low_balance"/>
        <result property="balanceStatusDesc" column="balance_status_desc"/>
    </resultMap>

    <select id="selectBalanceVoByMemberId" parameterType="Long" resultMap="BillingBalanceVoResult">
        select
            b.id,
            b.member_id,
            b.balance,
            b.frozen_amount,
            (b.balance + b.frozen_amount) as total_balance,
            COALESCE(c.symbol, '¥') as currency_symbol,
            COALESCE(c.code, 'CNY') as currency_code,
            b.low_threshold,
            CASE
                WHEN b.low_threshold IS NOT NULL AND b.balance < b.low_threshold THEN TRUE
                ELSE FALSE
            END as is_low_balance,
            CASE
                WHEN b.low_threshold IS NOT NULL AND b.balance < b.low_threshold THEN '余额不足'
                ELSE '余额充足'
            END as balance_status_desc
        from a_billing_balance b
        left join a_base_currency c on b.currency_id = c.id and c.delete_time is null
        where b.member_id = #{memberId} and b.delete_time is null
    </select>

</mapper>