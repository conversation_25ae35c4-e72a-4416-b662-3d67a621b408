package ai.showlab.bff.entity.domain.v1.billing;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 用户余额实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingBalance implements Serializable {
    /**
     * 序列化版本ID
     */
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 货币ID
     */
    private Long currencyId;

    /**
     * 余额预警阈值
     */
    private BigDecimal lowThreshold;

    /**
     * 创建时间
     */
    private OffsetDateTime createTime;

    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
}