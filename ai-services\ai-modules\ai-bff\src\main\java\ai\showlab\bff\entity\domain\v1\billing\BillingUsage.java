package ai.showlab.bff.entity.domain.v1.billing;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 用量记录实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingUsage implements Serializable {
    /**
     * 序列化版本ID
     */
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 计费方案ID
     */
    private Long planId;

    /**
     * 计费单位 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)
     */
    private Integer unit;

    /**
     * 使用量
     */
    private BigDecimal amount;

    /**
     * 处理时长 (毫秒)
     */
    private Integer durationMs;

    /**
     * 结果大小 (如字节)
     */
    private Integer resultSize;

    /**
     * 使用时间
     */
    private OffsetDateTime usedTime;
} 