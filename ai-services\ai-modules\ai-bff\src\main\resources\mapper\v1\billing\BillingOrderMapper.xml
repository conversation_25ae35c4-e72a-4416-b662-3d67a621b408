<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingOrderMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingOrder" id="BillingOrderResult">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="memberId" column="member_id"/>
        <result property="packageId" column="package_id"/>
        <result property="amount" column="amount"/>
        <result property="currencyId" column="currency_id"/>
        <result property="status" column="status"/>
        <result property="paymentGatewayId" column="payment_gateway_id"/>
        <result property="paidTime" column="paid_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectBillingOrderVo">
        select id, order_no, member_id, package_id, amount, currency_id, status,
               payment_gateway_id, paid_time, create_time
        from a_billing_order
    </sql>

    <select id="selectOrdersByMemberId" parameterType="Long" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        where member_id = #{memberId} and delete_time is null
        order by create_time desc
    </select>

    <select id="selectOrderByOrderNo" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        where order_no = #{orderNo}
          and member_id = #{memberId}
          and delete_time is null
    </select>

    <insert id="insertOrder" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingOrder" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_order (order_no, member_id, package_id, package_info_snapshot, amount, currency_id, status, create_time, update_time)
        values (#{orderNo}, #{memberId}, #{packageId}, #{packageInfoSnapshot}, #{amount}, #{currencyId}, #{status}, now(), now())
    </insert>

    <update id="updateOrderStatus">
        update a_billing_order
        set status = #{status},
            payment_gateway_id = #{paymentGatewayId},
            gateway_transaction_id = #{gatewayTransactionId},
            paid_time = now(),
            update_time = now()
        where order_no = #{orderNo}
          and member_id = #{memberId}
          and status = 1 -- 只能更新待支付的订单
          and delete_time is null
    </update>

    <select id="selectOrdersByMemberIdAndCondition" resultMap="BillingOrderResult">
        <include refid="selectBillingOrderVo"/>
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.status != null">
                and status = #{param.status}
            </if>
            <if test="param.startTime != null">
                and create_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and create_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and amount &lt;= #{param.maxAmount}
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by amount
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <when test="param.sortBy == 'paid_time'">
                order by paid_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by create_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

    <select id="countOrdersByMemberIdAndCondition" resultType="long">
        select count(*)
        from a_billing_order
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.status != null">
                and status = #{param.status}
            </if>
            <if test="param.startTime != null">
                and create_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and create_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and amount &lt;= #{param.maxAmount}
            </if>
        </where>
    </select>

    <!-- 直接查询订单VO，包含关联的套餐信息、货币信息和状态描述 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.BillingOrderVo" id="BillingOrderVoResult">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="packageName" column="package_name"/>
        <result property="amount" column="amount"/>
        <result property="currencySymbol" column="currency_symbol"/>
        <result property="status" column="status"/>
        <result property="statusDesc" column="status_desc"/>
        <result property="paidTime" column="paid_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectOrderVosByMemberIdAndCondition" resultMap="BillingOrderVoResult">
        select
            o.id,
            o.order_no,
            COALESCE(p.name,
                CASE
                    WHEN o.package_info_snapshot IS NOT NULL THEN
                        COALESCE(
                            (o.package_info_snapshot::json->>'name')::text,
                            '未知套餐'
                        )
                    ELSE '未知套餐'
                END
            ) as package_name,
            o.amount,
            COALESCE(c.symbol, '¥') as currency_symbol,
            o.status,
            COALESCE(os.dict_label, '未知状态') as status_desc,
            o.paid_time,
            o.create_time
        from a_billing_order o
        left join a_billing_package p on o.package_id = p.id and p.delete_time is null
        left join a_base_currency c on o.currency_id = c.id and c.delete_time is null
        left join sys_dict_data os on os.dict_type = 'order_status' and os.dict_value = CAST(o.status as VARCHAR) and os.status = '0'
        <where>
            and o.member_id = #{memberId}
            and o.delete_time is null
            <if test="param.status != null">
                and o.status = #{param.status}
            </if>
            <if test="param.startTime != null">
                and o.create_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and o.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and o.amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and o.amount &lt;= #{param.maxAmount}
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by o.amount
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <when test="param.sortBy == 'paid_time'">
                order by o.paid_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by o.create_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

</mapper>