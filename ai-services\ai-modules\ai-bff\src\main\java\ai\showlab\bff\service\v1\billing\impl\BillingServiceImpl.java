package ai.showlab.bff.service.v1.billing.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.domain.v1.billing.*;
import ai.showlab.bff.entity.domain.v1.member.Member;
import ai.showlab.bff.entity.dto.v1.BillingUsageDto;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.mapper.v1.billing.*;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.base.IBaseCurrencyService;
import ai.showlab.bff.service.v1.billing.IBillingService;
import ai.showlab.bff.service.v1.member.IMemberService;
import ai.showlab.bff.service.v1.model.IModelService;
import ai.showlab.bff.service.v1.sys.ISysDictService;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.core.web.page.PageResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * billing 服务
 *
 * <AUTHOR>
 */
@Service
public class BillingServiceImpl extends BaseService implements IBillingService {

    private static final Logger log = LoggerFactory.getLogger(BillingServiceImpl.class);

    @Autowired
    private BillingPackageMapper billingPackageMapper;
    @Autowired
    private BillingOrderMapper billingOrderMapper;
    @Autowired
    private BillingBalanceMapper billingBalanceMapper;
    @Autowired
    private BillingTransactionMapper billingTransactionMapper;
    @Autowired
    private BillingPriceMapper priceMapper;
    @Autowired
    private BillingUsageMapper billingUsageMapper;
    @Autowired
    private BillingPlanMapper billingPlanMapper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private IMemberService memberService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private IBaseCurrencyService baseCurrencyService;
    @Autowired
    private IModelService modelService;

    @Override
    @CustomCache(value = CacheConstants.BFF_BILLING_PACKAGES_KEY, key = "'available'", unless = "#result == null or #result.isEmpty()")
    public List<BillingPackage> getAvailablePackages() {
        return billingPackageMapper.selectAvailablePackages();
    }

    @Override
    @Transactional
    public BillingOrder createOrder(Long memberId, Long packageId) {
        BillingPackage pkg = billingPackageMapper.selectPackageById(packageId);
        if (pkg == null) {
            throw new ServiceException("计费套餐不存在");
        }

        BillingOrder order = new BillingOrder();
        order.setOrderNo("ORD_" + UUID.randomUUID().toString().replace("-", ""));
        order.setMemberId(memberId);
        order.setPackageId(packageId);
        order.setAmount(pkg.getPrice());
        order.setCurrencyId(pkg.getCurrencyId());
        // TODO: 1: 待支付
        order.setStatus(1);
        try {
            order.setPackageInfoSnapshot(objectMapper.writeValueAsString(pkg));
        } catch (JsonProcessingException e) {
            throw new ServiceException("创建订单失败：无法序列化套餐快照");
        }
        billingOrderMapper.insertOrder(order);
        return order;
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#usageDto.memberId")
    public void recordUsage(BillingUsageDto usageDto) {
        // 1. 获取会员等级
        Integer memberLevel = getMemberLevel(usageDto.getMemberId());

        // 2. 获取计费单价
        BillingPrice price = priceMapper.selectPriceByModelAndLevel(usageDto.getModelId(), memberLevel);
        if (price == null) {
            throw new ServiceException("当前模型或会员等级无可用计费策略");
        }

        // 3. 计算费用
        BigDecimal cost = price.getPrice().multiply(usageDto.getAmount());

        // 4. 检查并扣除余额
        BillingBalance balance = billingBalanceMapper.selectBalanceByMemberId(usageDto.getMemberId());
        if (balance == null || balance.getBalance().compareTo(cost) < 0) {
            throw new ServiceException("余额不足");
        }
        int updated = billingBalanceMapper.updateBalance(usageDto.getMemberId(), cost.negate());
        if (updated == 0) {
            throw new ServiceException("扣除余额失败");
        }

        // 5. 记录用量 (Usage)
        BillingUsage usage = new BillingUsage();
        BeanUtils.copyProperties(usageDto, usage);
        // 假设价格和方案关联
        usage.setPlanId(price.getPlanId());
        usage.setUsedTime(OffsetDateTime.now());
        billingUsageMapper.insertUsage(usage);

        // 6. 记录交易流水 (Transaction)
        BillingTransaction transaction = new BillingTransaction();
        transaction.setMemberId(usageDto.getMemberId());
        // TODO:  1: 消费
        transaction.setType(1);
        transaction.setAmount(cost.negate());
        transaction.setCurrencyId(price.getCurrencyId());
        // 关联用量记录ID
        transaction.setReferenceId(usage.getId());
        transaction.setDescription("使用模型: " + usageDto.getModelId());
        transaction.setTransactionTime(OffsetDateTime.now());
        billingTransactionMapper.insertTransaction(transaction);
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#order.memberId")
    public void handlePaymentSuccess(String orderNo, Long paymentGatewayId, String gatewayTransactionId) {
        // 此处需要加锁，防止并发问题。支付回调不一定有用户信息，先查出来
        BillingOrder order = billingOrderMapper.selectOrderByOrderNo(orderNo, null);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        if (order.getStatus() != 1) {
            // 可能已经处理过，直接返回成功
            return;
        }
        // 2: 已完成
        order.setStatus(2);
        order.setPaymentGatewayId(paymentGatewayId);
        order.setGatewayTransactionId(gatewayTransactionId);
        int updated = billingOrderMapper.updateOrderStatus(order);
        if (updated == 0) {
            throw new ServiceException("更新订单状态失败");
        }

        // 增加用户余额
        BillingPackage pkg;
        try {
            pkg = objectMapper.readValue(order.getPackageInfoSnapshot(), BillingPackage.class);
        } catch (JsonProcessingException e) {
            throw new ServiceException("处理支付失败：无法解析套餐快照");
        }
        billingBalanceMapper.updateBalance(order.getMemberId(), pkg.getCreditsGranted());

        // 记录交易流水
        BillingTransaction transaction = new BillingTransaction();
        transaction.setMemberId(order.getMemberId());
        // TODO:  2: 充值
        transaction.setType(2);
        transaction.setAmount(order.getAmount());
        transaction.setCurrencyId(order.getCurrencyId());
        transaction.setReferenceId(order.getId());
        transaction.setDescription("购买套餐: " + pkg.getName());
        transaction.setTransactionTime(OffsetDateTime.now());
        billingTransactionMapper.insertTransaction(transaction);
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#memberId")
    public BillingBalance getMemberBalance(Long memberId) {
        BillingBalance balance = billingBalanceMapper.selectBalanceByMemberId(memberId);
        if (balance == null) {
            // 如果用户是第一次查询余额，可能数据库中没有记录，这里初始化一个
            balance = new BillingBalance();
            balance.setMemberId(memberId);
            balance.setBalance(BigDecimal.ZERO);
            balance.setFrozenAmount(BigDecimal.ZERO);
        }
        return balance;
    }

    @Override
    public PageInfo<BillingOrderVo> getMemberOrders(Long memberId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BillingOrder> orders = billingOrderMapper.selectOrdersByMemberId(memberId);
        PageInfo<BillingOrder> pageInfo = new PageInfo<>(orders);

        List<BillingOrderVo> voList = orders.stream().map(o -> {
            BillingOrderVo vo = new BillingOrderVo();
            BeanUtils.copyProperties(o, vo);
            try {
                BillingPackage pkg = objectMapper.readValue(o.getPackageInfoSnapshot(), BillingPackage.class);
                vo.setPackageName(pkg.getName());
            } catch (JsonProcessingException e) {
                vo.setPackageName("未知套餐");
            }
            vo.setStatusDesc(convertOrderStatus(o.getStatus()));
            return vo;
        }).collect(Collectors.toList());

        PageInfo<BillingOrderVo> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        return voPageInfo;
    }

    @Override
    public PageInfo<BillingTransactionVo> getMemberTransactions(Long memberId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<BillingTransaction> transactions = billingTransactionMapper.selectTransactionsByMemberId(memberId);
        PageInfo<BillingTransaction> pageInfo = new PageInfo<>(transactions);

        List<BillingTransactionVo> voList = transactions.stream().map(t -> {
            BillingTransactionVo vo = new BillingTransactionVo();
            BeanUtils.copyProperties(t, vo);
            vo.setTypeDesc(convertTransactionType(t.getType()));
            return vo;
        }).collect(Collectors.toList());

        PageInfo<BillingTransactionVo> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        return voPageInfo;
    }

    private Integer getMemberLevel(Long memberId) {
        // 调用会员服务获取会员信息
        Member member = memberService.getMemberProfileById(memberId);
        if (member == null) {
            throw new ServiceException("会员不存在");
        }
        return member.getMemberLevel();
    }

    /**
     * 根据订单状态值获取状态描述
     * 使用数据字典 order_status 获取状态描述
     *
     * @param status 订单状态值
     * @return 状态描述
     */
    private String convertOrderStatus(Integer status) {
        if (status == null) {
            return "未知";
        }

        try {
            // 从数据字典获取订单状态列表
            var dictDataList = sysDictService.getDictData("order_status");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(status).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取订单状态字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知状态";
    }

    /**
     * 根据交易类型值获取类型描述
     * 使用数据字典 transaction_type 获取类型描述
     *
     * @param type 交易类型值
     * @return 类型描述
     */
    private String convertTransactionType(Integer type) {
        if (type == null) {
            return "未知";
        }

        try {
            // 从数据字典获取交易类型列表
            var dictDataList = sysDictService.getDictData("transaction_type");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(type).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取交易类型字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知类型";
    }

    // ==================== 面向外部用户的接口方法实现 ====================

    @Override
    public PageResult<BillingPackageVo> getPackageList(RequestParams<BillingPackageListParam> requestParams) {
        try {
            // 1. 获取参数
            BillingPackageListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new BillingPackageListParam();
            }

            // 2. 直接分页查询套餐VO列表（包含关联数据）
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            List<BillingPackageVo> voList = billingPackageMapper.selectPackageVosByCondition(param);
            PageInfo<BillingPackageVo> pageInfo = new PageInfo<>(voList);

            // 3. 构建分页结果
            PageResult<BillingPackageVo> result = new PageResult<>();
            result.setList(voList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            result.setHasNext(pageInfo.isHasNextPage());
            result.setHasPrevious(pageInfo.isHasPreviousPage());

            return result;
        } catch (Exception e) {
            log.error("获取套餐列表失败", e);
            throw new BusinessException("获取套餐列表失败，请稍后重试");
        }
    }

    @Override
    @Transactional
    public BillingOrderVo createOrderForMember(RequestParams<BillingOrderCreateParam> requestParams) {
        try {
            // 1. 获取当前会员ID
            Long currentMemberId = getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 2. 获取参数并验证套餐是否存在
            BillingOrderCreateParam param = requestParams.getBizParam();
            BillingPackage billingPackage = billingPackageMapper.selectPackageById(param.getPackageId());
            if (billingPackage == null) {
                throw new BusinessException(CodeConstants.NOT_FOUND, "套餐不存在");
            }

            // 3. 创建订单
            BillingOrder order = new BillingOrder();
            order.setOrderNo(generateOrderNo());
            order.setMemberId(currentMemberId);
            order.setPackageId(param.getPackageId());
            order.setAmount(billingPackage.getPrice());
            order.setCurrencyId(billingPackage.getCurrencyId());
            // 1-待支付
            order.setStatus(1);
            order.setPaymentGatewayId(param.getPaymentGatewayId());
            order.setCreateTime(OffsetDateTime.now());

            int result = billingOrderMapper.insertOrder(order);
            if (result == 0) {
                throw new BusinessException("创建订单失败，请稍后重试");
            }

            // 4. 转换为VO对象并返回
            return convertToOrderVo(order, billingPackage);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new BusinessException("创建订单失败，请稍后重试");
        }
    }

    @Override
    public PageResult<BillingOrderVo> getMemberOrderList(RequestParams<BillingOrderListParam> requestParams) {
        try {
            // 1. 获取当前会员ID
            Long currentMemberId = getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 2. 获取参数
            BillingOrderListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new BillingOrderListParam();
            }

            // 3. 直接分页查询订单VO列表（包含关联数据）
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            List<BillingOrderVo> voList = billingOrderMapper.selectOrderVosByMemberIdAndCondition(currentMemberId, param);
            PageInfo<BillingOrderVo> pageInfo = new PageInfo<>(voList);

            // 4. 构建分页结果
            PageResult<BillingOrderVo> result = new PageResult<>();
            result.setList(voList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            result.setHasNext(pageInfo.isHasNextPage());
            result.setHasPrevious(pageInfo.isHasPreviousPage());

            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            throw new BusinessException("获取订单列表失败，请稍后重试");
        }
    }

    @Override
    public BillingBalanceVo getCurrentMemberBalance() {
        try {
            // 1. 获取当前会员ID
            Long currentMemberId = getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 2. 直接查询余额VO信息（包含关联数据）
            BillingBalanceVo balanceVo = billingBalanceMapper.selectBalanceVoByMemberId(currentMemberId);
            if (balanceVo == null) {
                // 如果没有余额记录，创建一个默认的
                BillingBalance balance = createDefaultBalance(currentMemberId);
                balanceVo = billingBalanceMapper.selectBalanceVoByMemberId(currentMemberId);
            }

            // 3. 返回VO对象
            return balanceVo;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取余额信息失败", e);
            throw new BusinessException("获取余额信息失败，请稍后重试");
        }
    }

    @Override
    public PageResult<BillingTransactionVo> getMemberTransactionList(RequestParams<BillingTransactionListParam> requestParams) {
        try {
            // 1. 获取当前会员ID
            Long currentMemberId = getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 2. 获取参数
            BillingTransactionListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new BillingTransactionListParam();
            }

            // 3. 直接分页查询交易记录VO列表（包含关联数据）
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            List<BillingTransactionVo> voList = billingTransactionMapper.selectTransactionVosByMemberIdAndCondition(currentMemberId, param);
            PageInfo<BillingTransactionVo> pageInfo = new PageInfo<>(voList);

            // 4. 构建分页结果
            PageResult<BillingTransactionVo> result = new PageResult<>();
            result.setList(voList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            result.setHasNext(pageInfo.isHasNextPage());
            result.setHasPrevious(pageInfo.isHasPreviousPage());

            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取交易记录失败", e);
            throw new BusinessException("获取交易记录失败，请稍后重试");
        }
    }

    @Override
    public PageResult<BillingUsageVo> getMemberUsageList(RequestParams<BillingUsageListParam> requestParams) {
        try {
            // 1. 获取当前会员ID
            Long currentMemberId = getCurrentMemberId();
            if (currentMemberId == null) {
                throw new BusinessException(CodeConstants.UNAUTHORIZED, "请先登录");
            }

            // 2. 获取参数
            BillingUsageListParam param = requestParams.getBizParam();
            if (param == null) {
                param = new BillingUsageListParam();
            }

            // 3. 直接分页查询使用记录VO列表（包含关联数据）
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            List<BillingUsageVo> voList = billingUsageMapper.selectUsageVosByMemberIdAndCondition(currentMemberId, param);
            PageInfo<BillingUsageVo> pageInfo = new PageInfo<>(voList);

            // 4. 构建分页结果
            PageResult<BillingUsageVo> result = new PageResult<>();
            result.setList(voList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            result.setHasNext(pageInfo.isHasNextPage());
            result.setHasPrevious(pageInfo.isHasPreviousPage());

            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取使用记录失败", e);
            throw new BusinessException("获取使用记录失败，请稍后重试");
        }
    }

    @Override
    public List<BillingPlanVo> getAllPlans() {
        try {
            // 直接查询计费方案VO列表（包含关联数据）
            return billingPlanMapper.selectAllPlanVos();
        } catch (Exception e) {
            log.error("获取计费方案失败", e);
            throw new BusinessException("获取计费方案失败，请稍后重试");
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 将BillingPackage转换为BillingPackageVo
     *
     * @param billingPackage 套餐实体
     * @return 套餐VO
     */
    private BillingPackageVo convertToPackageVo(BillingPackage billingPackage) {
        if (billingPackage == null) {
            return null;
        }

        BillingPackageVo vo = new BillingPackageVo();
        BeanUtils.copyProperties(billingPackage, vo);

        // 设置类型描述
        vo.setTypeDesc(convertPackageType(billingPackage.getType()));

        // 设置货币信息
        if (billingPackage.getCurrencyId() != null) {
            var currency = baseCurrencyService.getCurrencyById(billingPackage.getCurrencyId());
            if (currency != null) {
                vo.setCurrencySymbol(currency.getSymbol());
                vo.setCurrencyCode(currency.getCode());
            }
        }

        // 设置有效期描述
        vo.setValidityDesc(convertValidityDays(billingPackage.getValidityDays()));

        // 设置续订周期描述
        vo.setRenewalIntervalDesc(convertRenewalInterval(billingPackage.getRenewalIntervalUnit()));

        // 设置会员等级描述
        vo.setMemberLevelDesc(convertMemberLevel(billingPackage.getMemberLevelGrant()));

        // 设置推荐和热门标识（可以根据业务规则设置）
        // TODO: 根据业务规则设置
        vo.setIsRecommended(false);
        // TODO: 根据业务规则设置
        vo.setIsPopular(false);

        return vo;
    }

    /**
     * 将BillingOrder转换为BillingOrderVo（带套餐信息）
     *
     * @param order          订单实体
     * @param billingPackage 套餐实体
     * @return 订单VO
     */
    private BillingOrderVo convertToOrderVo(BillingOrder order, BillingPackage billingPackage) {
        if (order == null) {
            return null;
        }

        BillingOrderVo vo = new BillingOrderVo();
        BeanUtils.copyProperties(order, vo);

        // 设置状态描述
        vo.setStatusDesc(convertOrderStatus(order.getStatus()));

        // 设置套餐名称
        if (billingPackage != null) {
            vo.setPackageName(billingPackage.getName());
        }

        // 设置货币符号
        if (order.getCurrencyId() != null) {
            var currency = baseCurrencyService.getCurrencyById(order.getCurrencyId());
            if (currency != null) {
                vo.setCurrencySymbol(currency.getSymbol());
            }
        }

        return vo;
    }

    /**
     * 将BillingOrder转换为BillingOrderVo（不带套餐信息）
     *
     * @param order 订单实体
     * @return 订单VO
     */
    private BillingOrderVo convertToOrderVo(BillingOrder order) {
        if (order == null) {
            return null;
        }

        // 查询套餐信息
        BillingPackage billingPackage = null;
        if (order.getPackageId() != null) {
            billingPackage = billingPackageMapper.selectPackageById(order.getPackageId());
        }

        return convertToOrderVo(order, billingPackage);
    }

    /**
     * 将BillingBalance转换为BillingBalanceVo
     *
     * @param balance 余额实体
     * @return 余额VO
     */
    private BillingBalanceVo convertToBalanceVo(BillingBalance balance) {
        if (balance == null) {
            return null;
        }

        BillingBalanceVo vo = new BillingBalanceVo();
        BeanUtils.copyProperties(balance, vo);

        // 计算总余额
        BigDecimal totalBalance = balance.getBalance().add(balance.getFrozenAmount());
        vo.setTotalBalance(totalBalance);

        // 设置货币信息
        if (balance.getCurrencyId() != null) {
            var currency = baseCurrencyService.getCurrencyById(balance.getCurrencyId());
            if (currency != null) {
                vo.setCurrencySymbol(currency.getSymbol());
                vo.setCurrencyCode(currency.getCode());
            }
        }

        // 判断是否余额不足
        boolean isLowBalance = balance.getLowThreshold() != null &&
                balance.getBalance().compareTo(balance.getLowThreshold()) < 0;
        vo.setIsLowBalance(isLowBalance);

        // 设置余额状态描述
        vo.setBalanceStatusDesc(isLowBalance ? "余额不足" : "余额充足");

        return vo;
    }

    /**
     * 将BillingTransaction转换为BillingTransactionVo
     *
     * @param transaction 交易记录实体
     * @return 交易记录VO
     */
    private BillingTransactionVo convertToTransactionVo(BillingTransaction transaction) {
        if (transaction == null) {
            return null;
        }

        BillingTransactionVo vo = new BillingTransactionVo();
        BeanUtils.copyProperties(transaction, vo);

        // 设置类型描述
        vo.setTypeDesc(convertTransactionType(transaction.getType()));

        // 设置货币符号
        if (transaction.getCurrencyId() != null) {
            var currency = baseCurrencyService.getCurrencyById(transaction.getCurrencyId());
            if (currency != null) {
                vo.setCurrencySymbol(currency.getSymbol());
            }
        }

        return vo;
    }

    /**
     * 将BillingUsage转换为BillingUsageVo
     *
     * @param usage 使用记录实体
     * @return 使用记录VO
     */
    private BillingUsageVo convertToUsageVo(BillingUsage usage) {
        if (usage == null) {
            return null;
        }

        BillingUsageVo vo = new BillingUsageVo();
        BeanUtils.copyProperties(usage, vo);

        // 设置模型信息
        if (usage.getModelId() != null) {
            try {
                var model = modelService.getModelById(usage.getModelId());
                if (model != null) {
                    vo.setModelName(model.getName());
                    vo.setModelCode(model.getCode());
                }
            } catch (Exception e) {
                log.warn("获取模型信息失败: modelId={}", usage.getModelId(), e);
            }
        }

        // 设置计费方案信息
        if (usage.getPlanId() != null) {
            var plan = billingPlanMapper.selectPlanById(usage.getPlanId());
            if (plan != null) {
                vo.setPlanName(plan.getName());
            }
        }

        // 设置计费单位描述
        vo.setUnitDesc(convertBillingUnit(usage.getUnit()));
        // 设置耗时描述
        vo.setDurationDesc(convertDuration(usage.getDurationMs()));
        // 设置结果大小描述
        vo.setResultSizeDesc(convertFileSize(usage.getResultSize()));

        // TODO: 计算本次使用的费用（需要根据计费方案和使用量计算）
        vo.setCost(BigDecimal.ZERO);
        // 默认货币符号
        vo.setCurrencySymbol("¥");

        return vo;
    }


    /**
     * 创建默认余额记录
     *
     * @param memberId 会员ID
     * @return 默认余额记录
     */
    private BillingBalance createDefaultBalance(Long memberId) {
        try {
            BillingBalance balance = new BillingBalance();
            balance.setMemberId(memberId);
            balance.setBalance(BigDecimal.ZERO);
            balance.setFrozenAmount(BigDecimal.ZERO);

            // 获取默认货币
            var defaultCurrency = baseCurrencyService.getDefaultCurrency();
            if (defaultCurrency != null) {
                balance.setCurrencyId(defaultCurrency.getId());
            }
            // 默认预警阈值
            balance.setLowThreshold(new BigDecimal("10.00"));
            balance.setCreateTime(OffsetDateTime.now());
            balance.setUpdateTime(OffsetDateTime.now());

            // 插入数据库
            int result = billingBalanceMapper.insertBalance(balance);
            if (result > 0) {
                return balance;
            } else {
                throw new BusinessException("创建默认余额记录失败");
            }
        } catch (Exception e) {
            log.error("创建默认余额记录失败: memberId={}", memberId, e);
            throw new BusinessException("创建默认余额记录失败");
        }
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    private String generateOrderNo() {
        // 格式: ORD + 年月日 + 时分秒 + 3位随机数
        String timestamp = OffsetDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 900) + 100; // 100-999的随机数
        return "ORD" + timestamp + random;
    }

    /**
     * 转换套餐类型为描述
     *
     * @param type 套餐类型
     * @return 类型描述
     */
    private String convertPackageType(Integer type) {
        if (type == null) {
            return "未知";
        }

        try {
            // 从数据字典获取套餐类型列表
            var dictDataList = sysDictService.getDictData("package_type");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(type).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取套餐类型字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知类型";
    }

    /**
     * 转换有效期天数为描述
     *
     * @param validityDays 有效期天数
     * @return 有效期描述
     */
    private String convertValidityDays(Integer validityDays) {
        if (validityDays == null || validityDays <= 0) {
            return "永久有效";
        }

        if (validityDays == 30) {
            return "30天有效";
        } else if (validityDays == 365) {
            return "1年有效";
        } else if (validityDays % 30 == 0) {
            return (validityDays / 30) + "个月有效";
        } else {
            return validityDays + "天有效";
        }
    }

    /**
     * 转换续订周期为描述
     *
     * @param renewalIntervalUnit 续订周期单位
     * @return 续订周期描述
     */
    private String convertRenewalInterval(Integer renewalIntervalUnit) {
        if (renewalIntervalUnit == null) {
            return "一次性";
        }

        try {
            // 从数据字典获取续订周期列表
            var dictDataList = sysDictService.getDictData("renewal_interval_unit");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(renewalIntervalUnit).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取续订周期字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知周期";
    }

    /**
     * 转换会员等级为描述
     *
     * @param memberLevel 会员等级
     * @return 会员等级描述
     */
    private String convertMemberLevel(Integer memberLevel) {
        if (memberLevel == null) {
            return "普通会员";
        }

        try {
            // 从数据字典获取会员等级列表
            var dictDataList = sysDictService.getDictData("member_level");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(memberLevel).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取会员等级字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知等级";
    }

    /**
     * 转换计费单位为描述
     *
     * @param unit 计费单位
     * @return 计费单位描述
     */
    private String convertBillingUnit(Integer unit) {
        if (unit == null) {
            return "未知";
        }

        try {
            // 从数据字典获取计费单位列表
            var dictDataList = sysDictService.getDictData("billing_unit");
            if (dictDataList != null) {
                // 查找匹配的字典项
                for (var dictData : dictDataList) {
                    if (String.valueOf(unit).equals(dictData.getDictValue())) {
                        return dictData.getDictLabel();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取计费单位字典失败，使用默认值: {}", e.getMessage());
        }

        // 如果字典中没有找到，返回默认值
        return "未知单位";
    }

    /**
     * 转换耗时毫秒数为描述
     *
     * @param durationMs 耗时毫秒数
     * @return 耗时描述
     */
    private String convertDuration(Integer durationMs) {
        if (durationMs == null || durationMs <= 0) {
            return "0秒";
        }

        if (durationMs < 1000) {
            return durationMs + "毫秒";
        } else if (durationMs < 60000) {
            double seconds = durationMs / 1000.0;
            return String.format("%.1f秒", seconds);
        } else {
            int minutes = durationMs / 60000;
            int seconds = (durationMs % 60000) / 1000;
            return minutes + "分" + seconds + "秒";
        }
    }

    /**
     * 转换文件大小字节数为描述
     *
     * @param sizeBytes 文件大小字节数
     * @return 文件大小描述
     */
    private String convertFileSize(Integer sizeBytes) {
        if (sizeBytes == null || sizeBytes <= 0) {
            return "0B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = sizeBytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        if (unitIndex == 0) {
            return (int) size + units[unitIndex];
        } else {
            return String.format("%.1f%s", size, units[unitIndex]);
        }
    }
}