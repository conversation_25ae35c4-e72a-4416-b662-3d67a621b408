package ai.showlab.bff.mapper.v1.billing;

import ai.showlab.bff.entity.domain.v1.billing.BillingPlan;
import ai.showlab.bff.entity.vo.v1.BillingPlanVo;

import java.util.List;

/**
 * 计费方案 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BillingPlanMapper {

    /**
     * 查询所有计费方案
     *
     * @return 计费方案列表
     */
    List<BillingPlan> selectAllPlans();

    /**
     * 根据ID查询计费方案
     *
     * @param id 计费方案ID
     * @return 计费方案信息
     */
    BillingPlan selectPlanById(Long id);

    /**
     * 直接查询所有计费方案VO列表（包含关联数据）
     *
     * @return 计费方案VO列表
     */
    List<BillingPlanVo> selectAllPlanVos();

}