<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingPlanMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingPlan" id="BillingPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="unit" column="unit"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="description" column="description"/>
    </resultMap>

    <sql id="selectBillingPlanVo">
        select id, name, unit, sort_order, description
        from a_billing_plan
    </sql>

    <select id="selectAllPlans" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        where delete_time is null
        order by sort_order asc
    </select>

    <select id="selectPlanById" parameterType="Long" resultMap="BillingPlanResult">
        <include refid="selectBillingPlanVo"/>
        where id = #{id} and delete_time is null
    </select>

    <!-- 直接查询计费方案VO列表（包含关联数据） -->
    <select id="selectAllPlanVos" resultType="ai.showlab.bff.entity.vo.v1.BillingPlanVo">
        SELECT
            bp.id,
            bp.name,
            bp.unit,
            COALESCE(sdd.dict_label, '未知') as unitDesc,
            bp.sort_order as sortOrder,
            bp.description
        FROM a_billing_plan bp
        LEFT JOIN sys_dict_data sdd ON sdd.dict_type = 'billing_unit'
                                    AND sdd.dict_value = CAST(bp.unit AS VARCHAR)
                                    AND sdd.status = '0'
        WHERE bp.delete_time IS NULL
        ORDER BY bp.sort_order ASC
    </select>

</mapper>