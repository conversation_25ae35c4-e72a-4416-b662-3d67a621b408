# AI-BFF 异常处理最佳实践

## 概述

本文档描述了AI-BFF模块中异常处理的最佳实践，旨在提供统一、清晰、易维护的异常处理机制。

## 异常处理架构

### 1. 异常类型层次

```
Throwable
├── Error (系统错误，不处理)
├── Exception
    ├── RuntimeException
    │   └── BusinessException (业务异常，主要使用)
    └── CheckedException (受检异常，尽量避免)
```

### 2. 异常处理层次

```
Controller层 → Service层 → Mapper层 → 数据库
     ↓           ↓          ↓
统一异常处理  BusinessException  SQLException等
```

## 异常处理原则

### 1. 优先使用BusinessException

**推荐做法**：
```java
// Service接口
public interface IAssistantService {
    PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException;
}

// Service实现
@Override
public PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException {
    return BaseController.executeServiceOperation(() -> {
        // 业务逻辑
        return result;
    }, "获取助手列表失败");
}
```

**避免的做法**：
```java
// 不推荐：使用泛化的Exception
public PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws Exception;
```

### 2. Controller层异常处理

**推荐做法**：
```java
@PostMapping("/list")
public ResponseEntity<RestResult> getAssistantList(RequestParams<AssistantListParam> requestParams) {
    return executeWithTryCatch(() -> {
        return assistantService.getAssistantList(requestParams);
    }, "获取助手列表失败");
}
```

**说明**：
- Controller层使用`executeWithTryCatch`统一处理异常
- `BusinessOperation<T>`接口只抛出`BusinessException`
- 自动转换为标准的HTTP响应格式

### 3. Service层异常处理

**推荐做法**：
```java
@Override
public PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException {
    return BaseController.executeServiceOperation(() -> {
        // 可能抛出各种异常的业务逻辑
        List<AssistantListVo> list = assistantMapper.selectAssistantListVoByCondition(param);
        // 其他业务逻辑...
        return PageResult.of(list, total, param.getPageNum(), param.getPageSize());
    }, "获取助手列表失败");
}
```

**说明**：
- 使用`executeServiceOperation`包装可能抛出异常的代码
- 自动将各种异常转换为`BusinessException`
- 保持Service接口的简洁性

### 4. 直接抛出BusinessException

**适用场景**：业务逻辑验证失败
```java
@Override
public AssistantDetailVo getAssistantDetail(RequestParams<AssistantDetailParam> requestParams) throws BusinessException {
    AssistantDetailParam param = requestParams.getBizParam();
    Long assistantId = param.getAssistantId();
    
    AssistantDetailVo detail = assistantMapper.selectAssistantDetailVoById(assistantId);
    if (detail == null) {
        throw new BusinessException(CodeConstants.DATA_NOT_FOUND, "助手不存在");
    }
    
    return detail;
}
```

## 异常处理工具

### 1. BaseController.executeWithTryCatch()

**用途**：Controller层统一异常处理
**特点**：
- 处理`BusinessException`并转换为相应的HTTP状态码
- 处理其他异常并返回500错误
- 返回统一格式的`ResponseEntity<RestResult>`

### 2. BaseController.executeServiceOperation()

**用途**：Service层异常转换
**特点**：
- 将各种异常统一转换为`BusinessException`
- 保持`BusinessException`不变
- 记录详细的错误日志

### 3. BusinessException

**用途**：业务异常的标准表示
**特点**：
- 包含错误码和错误信息
- 支持国际化
- 可以携带额外的上下文信息

## 异常处理示例

### 1. 完整的CRUD操作异常处理

```java
// 接口定义
public interface IAssistantService {
    PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException;
    AssistantDetailVo getAssistantDetail(RequestParams<AssistantDetailParam> requestParams) throws BusinessException;
    void createAssistant(RequestParams<AssistantCreateParam> requestParams) throws BusinessException;
    void updateAssistant(RequestParams<AssistantUpdateParam> requestParams) throws BusinessException;
    void deleteAssistant(RequestParams<AssistantDeleteParam> requestParams) throws BusinessException;
}

// 实现类
@Service
public class AssistantServiceImpl implements IAssistantService {
    
    @Override
    public PageResult<AssistantListVo> getAssistantList(RequestParams<AssistantListParam> requestParams) throws BusinessException {
        return BaseController.executeServiceOperation(() -> {
            // 复杂的业务逻辑，可能抛出各种异常
            return performComplexQuery(requestParams);
        }, "获取助手列表失败");
    }
    
    @Override
    public AssistantDetailVo getAssistantDetail(RequestParams<AssistantDetailParam> requestParams) throws BusinessException {
        // 简单的业务逻辑，直接抛出BusinessException
        AssistantDetailParam param = requestParams.getBizParam();
        AssistantDetailVo detail = assistantMapper.selectAssistantDetailVoById(param.getAssistantId());
        
        if (detail == null) {
            throw new BusinessException(CodeConstants.DATA_NOT_FOUND, "助手不存在");
        }
        
        return detail;
    }
}

// Controller
@RestController
public class AssistantController extends BaseController {
    
    @PostMapping("/list")
    public ResponseEntity<RestResult> getAssistantList(RequestParams<AssistantListParam> requestParams) {
        return executeWithTryCatch(() -> {
            return assistantService.getAssistantList(requestParams);
        }, "获取助手列表失败");
    }
}
```

## 不需要修改的异常处理

### 1. Spring框架接口

某些Spring框架的接口方法必须保持原有的异常声明：

```java
// HandlerInterceptor接口要求throws Exception
@Override
public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
    // 实现逻辑
}

// AOP切面方法要求throws Throwable
@Around("@annotation(apiAuth)")
public Object around(ProceedingJoinPoint joinPoint, ApiAuth apiAuth) throws Throwable {
    // 实现逻辑
}
```

### 2. 函数式接口

某些内部使用的函数式接口可以保持`throws Exception`：

```java
@FunctionalInterface
public interface ServiceOperation<T> {
    T execute() throws Exception;  // 内部使用，由executeServiceOperation转换
}
```

## 迁移指南

### 1. 逐步迁移策略

1. **第一阶段**：更新核心Service接口，添加`throws BusinessException`
2. **第二阶段**：更新Service实现，使用新的异常处理工具
3. **第三阶段**：验证Controller层的异常处理是否正常工作
4. **第四阶段**：清理不必要的try-catch块

### 2. 迁移检查清单

- [ ] Service接口方法添加`throws BusinessException`
- [ ] Service实现使用`executeServiceOperation`或直接抛出`BusinessException`
- [ ] Controller使用`executeWithTryCatch`
- [ ] 移除不必要的异常捕获和转换代码
- [ ] 更新单元测试的异常期望

## 总结

通过使用`BusinessException`替代`throws Exception`，我们可以：

1. **提高代码质量**：明确的异常类型，更好的IDE支持
2. **简化异常处理**：统一的异常处理机制
3. **改善用户体验**：精确的错误信息和状态码
4. **便于维护**：清晰的异常处理流程
5. **支持国际化**：标准化的错误码和消息

这种方式既保持了异常处理的灵活性，又提供了统一的异常处理标准。
