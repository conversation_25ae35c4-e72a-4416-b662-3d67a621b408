<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingUsageMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingUsage" id="BillingUsageResult">
        <result property="id" column="id"/>
        <result property="memberId" column="member_id"/>
        <result property="modelId" column="model_id"/>
        <result property="planId" column="plan_id"/>
        <result property="unit" column="unit"/>
        <result property="amount" column="amount"/>
        <result property="durationMs" column="duration_ms"/>
        <result property="resultSize" column="result_size"/>
        <result property="usedTime" column="used_time"/>
    </resultMap>

    <sql id="selectBillingUsageVo">
        select id, member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time
        from a_billing_usage
    </sql>

    <select id="selectUsagesByMemberId" parameterType="Long" resultMap="BillingUsageResult">
        <include refid="selectBillingUsageVo"/>
        where member_id = #{memberId} and delete_time is null
        order by used_time desc
    </select>

    <insert id="insertUsage" parameterType="ai.showlab.bff.entity.domain.v1.billing.BillingUsage" useGeneratedKeys="true" keyProperty="id">
        insert into a_billing_usage (member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time, create_time, update_time)
        values (#{memberId}, #{modelId}, #{planId}, #{unit}, #{amount}, #{durationMs}, #{resultSize}, #{usedTime}, now(), now())
    </insert>

    <select id="selectUsagesByMemberIdAndCondition" resultMap="BillingUsageResult">
        <include refid="selectBillingUsageVo"/>
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.modelId != null">
                and model_id = #{param.modelId}
            </if>
            <if test="param.planId != null">
                and plan_id = #{param.planId}
            </if>
            <if test="param.unit != null">
                and unit = #{param.unit}
            </if>
            <if test="param.startTime != null">
                and used_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and used_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and amount &lt;= #{param.maxAmount}
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by amount
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <when test="param.sortBy == 'duration_ms'">
                order by duration_ms
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by used_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

    <select id="countUsagesByMemberIdAndCondition" resultType="long">
        select count(*)
        from a_billing_usage
        <where>
            and member_id = #{memberId}
            and delete_time is null
            <if test="param.modelId != null">
                and model_id = #{param.modelId}
            </if>
            <if test="param.planId != null">
                and plan_id = #{param.planId}
            </if>
            <if test="param.unit != null">
                and unit = #{param.unit}
            </if>
            <if test="param.startTime != null">
                and used_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and used_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and amount &lt;= #{param.maxAmount}
            </if>
        </where>
    </select>

    <!-- 直接查询使用记录VO，包含关联的模型信息、计费方案信息等 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.BillingUsageVo" id="BillingUsageVoResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="modelName" column="model_name"/>
        <result property="modelCode" column="model_code"/>
        <result property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="unit" column="unit"/>
        <result property="unitDesc" column="unit_desc"/>
        <result property="amount" column="amount"/>
        <result property="durationMs" column="duration_ms"/>
        <result property="durationDesc" column="duration_desc"/>
        <result property="resultSize" column="result_size"/>
        <result property="resultSizeDesc" column="result_size_desc"/>
        <result property="usedTime" column="used_time"/>
        <result property="cost" column="cost"/>
        <result property="currencySymbol" column="currency_symbol"/>
    </resultMap>

    <select id="selectUsageVosByMemberIdAndCondition" resultMap="BillingUsageVoResult">
        select
            u.id,
            u.model_id,
            COALESCE(m.name, '未知模型') as model_name,
            COALESCE(m.code, '') as model_code,
            u.plan_id,
            COALESCE(p.name, '未知方案') as plan_name,
            u.unit,
            COALESCE(bu.dict_label, '未知单位') as unit_desc,
            u.amount,
            u.duration_ms,
            CASE
                WHEN u.duration_ms IS NULL OR u.duration_ms <= 0 THEN '0秒'
                WHEN u.duration_ms < 1000 THEN CONCAT(u.duration_ms, '毫秒')
                WHEN u.duration_ms < 60000 THEN CONCAT(ROUND(u.duration_ms / 1000.0, 1), '秒')
                ELSE CONCAT(u.duration_ms / 60000, '分', (u.duration_ms % 60000) / 1000, '秒')
            END as duration_desc,
            u.result_size,
            CASE
                WHEN u.result_size IS NULL OR u.result_size <= 0 THEN '0B'
                WHEN u.result_size < 1024 THEN CONCAT(u.result_size, 'B')
                WHEN u.result_size < 1048576 THEN CONCAT(ROUND(u.result_size / 1024.0, 1), 'KB')
                WHEN u.result_size < 1073741824 THEN CONCAT(ROUND(u.result_size / 1048576.0, 1), 'MB')
                ELSE CONCAT(ROUND(u.result_size / 1073741824.0, 1), 'GB')
            END as result_size_desc,
            u.used_time,
            COALESCE(ABS(t.amount), 0) as cost,
            COALESCE(c.symbol, '¥') as currency_symbol
        from a_billing_usage u
        left join a_model m on u.model_id = m.id and m.delete_time is null
        left join a_billing_plan p on u.plan_id = p.id and p.delete_time is null
        left join sys_dict_data bu on bu.dict_type = 'billing_unit' and bu.dict_value = CAST(u.unit as VARCHAR) and bu.status = '0'
        left join a_billing_transaction t on t.reference_id = u.id and t.type = 1 and t.delete_time is null
        left join a_base_currency c on t.currency_id = c.id and c.delete_time is null
        <where>
            and u.member_id = #{memberId}
            and u.delete_time is null
            <if test="param.modelId != null">
                and u.model_id = #{param.modelId}
            </if>
            <if test="param.planId != null">
                and u.plan_id = #{param.planId}
            </if>
            <if test="param.unit != null">
                and u.unit = #{param.unit}
            </if>
            <if test="param.startTime != null">
                and u.used_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and u.used_time &lt;= #{param.endTime}
            </if>
            <if test="param.minAmount != null">
                and u.amount >= #{param.minAmount}
            </if>
            <if test="param.maxAmount != null">
                and u.amount &lt;= #{param.maxAmount}
            </if>
        </where>
        <choose>
            <when test="param.sortBy == 'amount'">
                order by u.amount
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <when test="param.sortBy == 'duration_ms'">
                order by u.duration_ms
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <when test="param.sortBy == 'cost'">
                order by COALESCE(ABS(t.amount), 0)
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by u.used_time
                <if test="param.sortOrder == 'desc'">desc</if>
                <if test="param.sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

</mapper>