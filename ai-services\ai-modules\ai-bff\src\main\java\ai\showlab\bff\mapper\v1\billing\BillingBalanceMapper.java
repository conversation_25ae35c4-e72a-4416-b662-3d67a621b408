package ai.showlab.bff.mapper.v1.billing;

import ai.showlab.bff.entity.domain.v1.billing.BillingBalance;
import ai.showlab.bff.entity.vo.v1.BillingBalanceVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 用户余额 Mapper 接口
 *
 * <AUTHOR>
 */
public interface BillingBalanceMapper {

    /**
     * 根据会员ID查询余额
     *
     * @param memberId 会员ID
     * @return 余额信息
     */
    BillingBalance selectBalanceByMemberId(Long memberId);

    /**
     * 新增余额记录
     *
     * @param balance 余额信息
     * @return 影响行数
     */
    int insertBalance(BillingBalance balance);

    /**
     * 更新余额
     *
     * @param memberId     会员ID
     * @param changeAmount 变动金额 (可正可负)
     * @return 影响行数
     */
    int updateBalance(@Param("memberId") Long memberId, @Param("changeAmount") BigDecimal changeAmount);

    /**
     * 更新冻结金额
     *
     * @param memberId     会员ID
     * @param changeAmount 变动金额 (可正可负)
     * @return 影响行数
     */
    int updateFrozenAmount(@Param("memberId") Long memberId, @Param("changeAmount") BigDecimal changeAmount);

    /**
     * 根据会员ID直接查询余额VO（包含关联的货币信息和状态描述）
     *
     * @param memberId 会员ID
     * @return 余额VO信息
     */
    BillingBalanceVo selectBalanceVoByMemberId(Long memberId);

}